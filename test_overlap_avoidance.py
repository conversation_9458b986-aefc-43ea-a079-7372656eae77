#!/usr/bin/env python3
"""
测试重叠避免功能的脚本
专门验证18级别和20级别Tile content重叠显示问题的修复效果
"""

import omni.usd
from pxr import Gf, UsdGeom
import math

def test_overlap_avoidance():
    """测试重叠避免功能"""
    
    print("🔍 测试18级别和20级别Tile Content重叠避免功能")
    print("=" * 80)
    
    # 获取stage和相机
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("❌ 无法获取USD stage")
        return
    
    camera_path = "/World/Camera"
    camera = stage.GetPrimAtPath(camera_path)
    if not camera:
        print(f"❌ 相机未找到: {camera_path}")
        return
    
    # 获取相机位置
    xformable = UsdGeom.Xformable(camera)
    transform = xformable.GetLocalTransformation()
    camera_position = Gf.Vec3f(transform.ExtractTranslation())
    print(f"📍 相机位置: {camera_position}")
    
    try:
        # 导入修复后的LOD系统
        from lod.lod_scheduler import TilesetLODManager, LODScheduler
        from lod.lod_config import get_default_lod_config
        
        # 使用默认配置
        lod_config = get_default_lod_config()
        
        # 创建LOD调度器和管理器
        lod_scheduler = LODScheduler(stage, camera_path, centralized_config=lod_config)
        tileset_manager = TilesetLODManager(
            stage=stage,
            camera_path=camera_path,
            lod_scheduler=lod_scheduler,
            config=lod_config
        )
        
        print("✅ LOD系统初始化成功")
        
        # 显示当前的LOD级别映射
        print("\n📋 当前LOD级别映射:")
        if hasattr(lod_scheduler, 'centralized_config'):
            error_to_lod = lod_scheduler.centralized_config.get_lod_mapping()
            print("  几何误差 -> LOD级别:")
            for error, lod_name in sorted(error_to_lod.items()):
                print(f"    {error} -> {lod_name}")
        
        # 执行LOD更新
        print("\n🔄 执行带重叠避免的LOD更新...")
        selected_lod, distance, avg_distance = tileset_manager.update_tileset_lod_visibility(verbose=True)
        
        if selected_lod:
            print(f"\n✅ LOD更新完成")
            print(f"   主要LOD级别: {selected_lod}")
            print(f"   相机到区域中心距离: {distance:.1f}m")
            print(f"   平均Tile距离: {avg_distance:.1f}m")
        else:
            print("❌ LOD更新失败")
            return
        
        # 分析重叠避免效果
        print("\n" + "=" * 80)
        print("🔍 重叠避免效果分析")
        print("=" * 80)
        
        print("1. 查看上面的日志，寻找以下关键信息:")
        print("   ✅ 绿色勾号：正常显示的Tiles")
        print("   🚫 红色叉号：被子Tiles抑制的父Tiles")
        print("   📊 Depth Distribution：各深度层级的可见Tiles数量")
        print("   🚫 Suppressed by children：被抑制的Tiles数量")
        
        print("\n2. 重叠避免机制:")
        print("   - 当子Tile选择了更高精度的LOD时，会抑制父Tile的显示")
        print("   - 只有在边界重叠且LOD优先级更高时才会抑制")
        print("   - LOD优先级: High(4) > Medium(3) > Low(2) > VeryLow(1)")
        
        print("\n3. 预期效果:")
        print("   - 18级别(Medium)和20级别(High)的重叠应该被避免")
        print("   - 距离近的区域优先显示High LOD")
        print("   - 距离远的区域显示Medium/Low LOD")
        print("   - 不应该出现同一空间区域的多个LOD级别同时显示")
        
        # 收集详细统计信息
        all_tiles = tileset_manager.collect_all_tiles()
        if all_tiles:
            print(f"\n📊 详细统计:")
            print(f"   总Tiles数量: {len(all_tiles)}")
            
            # 按深度分组统计
            depth_stats = {}
            for tile in all_tiles:
                depth = tile.get('depth', 0)
                if depth not in depth_stats:
                    depth_stats[depth] = {'total': 0, 'with_content': 0}
                depth_stats[depth]['total'] += 1
                if tile.get('content_nodes'):
                    depth_stats[depth]['with_content'] += 1
            
            print("   按深度分布:")
            for depth in sorted(depth_stats.keys()):
                stats = depth_stats[depth]
                print(f"     深度 {depth}: {stats['total']} tiles ({stats['with_content']} 有内容)")
        
        print("\n" + "=" * 80)
        print("🎯 验证要点")
        print("=" * 80)
        print("如果重叠避免功能正常工作，您应该看到:")
        print("✅ 有一些Tiles被标记为'SUPPRESSED by child tile'")
        print("✅ 不同深度的Tiles根据距离选择了不同的LOD级别")
        print("✅ 没有18级别和20级别content在同一区域同时显示")
        print("✅ Depth Distribution显示合理的深度分布")
        
        print("\n如果仍有重叠问题，可能需要:")
        print("🔧 调整重叠检测阈值")
        print("🔧 优化LOD优先级算法")
        print("🔧 检查边界框计算的准确性")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def explain_overlap_issue():
    """解释重叠问题的原因和解决方案"""
    
    print("\n" + "=" * 80)
    print("18级别和20级别重叠问题的原因分析")
    print("=" * 80)
    
    print("🔴 问题原因:")
    print("1. 原始代码使用固定的LOD级别映射:")
    print("   {'High': 20, 'Medium': 18, 'Low': 16, 'VeryLow': 14}")
    print("2. 但实际的几何误差配置是:")
    print("   {'High': [1,2], 'Medium': [4,8], 'Low': [16], 'VeryLow': [32]}")
    print("3. 这导致18级别被错误地映射为Medium LOD")
    print("4. 当父Tile选择Medium LOD，子Tile选择High LOD时，两者会重叠显示")
    
    print("\n🟢 解决方案:")
    print("1. 使用实际的几何误差值进行LOD级别映射")
    print("2. 实现层级感知的LOD匹配")
    print("3. 添加重叠检测和避免机制")
    print("4. 当子Tile选择更高精度LOD时，抑制父Tile的显示")
    
    print("\n🔧 关键改进:")
    print("- 动态构建LOD级别映射，而不是使用硬编码值")
    print("- 检查边界重叠和LOD优先级")
    print("- 按深度层级管理Tiles的可见性")
    print("- 提供详细的调试日志")

if __name__ == "__main__":
    print("🚀 启动重叠避免功能测试")
    
    # 解释问题和解决方案
    explain_overlap_issue()
    
    # 执行测试
    test_overlap_avoidance()
    
    print("\n🏁 测试完成！请根据上述日志判断重叠避免效果。")
