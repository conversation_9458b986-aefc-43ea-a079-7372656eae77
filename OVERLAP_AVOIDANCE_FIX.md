# 18级别和20级别Tile Content重叠显示问题修复

## 问题描述

在实现每个Tile独立LOD选择后，出现了新的问题：**18级别的Tile content会和其20级别的子Tile content重叠显示**。这种重叠会在部分Tile上出现，导致视觉上的冗余和性能浪费。

## 问题根本原因

### 1. LOD级别映射不一致
原始代码使用了硬编码的LOD级别映射：
```python
lod_name_to_level = {"High": 20, "Medium": 18, "Low": 16, "VeryLow": 14}
```

但实际的几何误差配置是：
```json
{
  "High": [1, 2],
  "Medium": [4, 8], 
  "Low": [16],
  "VeryLow": [32]
}
```

### 2. 缺乏层级感知机制
- 父Tile和子Tile独立选择LOD，没有考虑层级关系
- 当父Tile选择Medium LOD(18级别)，子Tile选择High LOD(20级别)时
- 如果两者在空间上重叠，就会同时显示，造成重叠

### 3. 空间重叠检测缺失
- 没有检测不同深度Tiles之间的空间重叠
- 没有实现基于LOD优先级的显示抑制机制

## 修复方案

### 1. 动态LOD级别映射
```python
# 从配置中获取实际的几何误差映射
if hasattr(self.lod_scheduler, 'centralized_config'):
    error_to_lod = self.lod_scheduler.centralized_config.get_lod_mapping()
    # 构建LOD名称到几何误差的映射
    lod_name_to_levels = {}
    for error, lod_name in error_to_lod.items():
        if lod_name not in lod_name_to_levels:
            lod_name_to_levels[lod_name] = []
        lod_name_to_levels[lod_name].append(int(error))
```

### 2. 层级感知的LOD匹配
```python
# 获取该LOD级别对应的所有几何误差级别
target_lod_levels = lod_name_to_all_levels.get(tile_selected_lod, [32])

# 层级感知的LOD匹配：检查content的LOD级别是否在目标范围内
lod_match = content_lod_level in target_lod_levels
```

### 3. 重叠检测和避免机制
```python
# 重叠避免逻辑：检查是否有更高精度的子Tiles在显示
should_suppress_parent = False
if tile_depth > 0:  # 不是根节点
    # 检查是否有子深度的Tiles选择了更高精度的LOD
    for child_depth in range(tile_depth + 1, max(depth_to_tiles.keys()) + 1):
        if child_depth in depth_to_tiles:
            for child_tile_info in depth_to_tiles[child_depth]:
                child_lod = child_tile_info['selected_lod']
                # 如果子Tile选择了更高精度的LOD，则抑制父Tile的显示
                lod_priority = {"High": 4, "Medium": 3, "Low": 2, "VeryLow": 1}
                if (lod_priority.get(child_lod, 1) > lod_priority.get(tile_selected_lod, 1) and
                    child_tile_info['bounds'] and tile_bounds and
                    self._bounds_overlap(child_tile_info['bounds'], tile_bounds)):
                    should_suppress_parent = True
```

### 4. 边界重叠检测
```python
def _bounds_overlap(self, bounds1: BoundingBox, bounds2: BoundingBox, threshold: float = 0.1) -> bool:
    """检查两个边界框是否重叠"""
    # 计算重叠区域
    overlap_min = Gf.Vec3f(
        max(bounds1.min_point[0], bounds2.min_point[0]),
        max(bounds1.min_point[1], bounds2.min_point[1]),
        max(bounds1.min_point[2], bounds2.min_point[2])
    )
    overlap_max = Gf.Vec3f(
        min(bounds1.max_point[0], bounds2.max_point[0]),
        min(bounds1.max_point[1], bounds2.max_point[1]),
        min(bounds1.max_point[2], bounds2.max_point[2])
    )
    
    # 检查重叠比例是否超过阈值
    if smaller_volume > 0:
        overlap_ratio = overlap_volume / smaller_volume
        return overlap_ratio > threshold
```

## 修复效果

### 预期行为
1. **18级别和20级别不再重叠显示**
2. **父Tile在子Tile显示高精度LOD时被自动抑制**
3. **空间上只显示最合适的LOD级别**
4. **提供详细的重叠避免日志**

### 日志输出示例
```
📊 Per-Tile LOD Selection Results with Overlap Avoidance:
  ✅  1. depth=1, tile_index= 0, Selected_LOD=High, Content_LOD=20, Distance=45.2m, tile=Tile_1_0_0
  🚫  2. depth=0, tile_index= 0, Selected_LOD=Medium, Content_LOD=18, SUPPRESSED by child tile, tile=Tile_0_0_0
  ✅  3. depth=2, tile_index= 1, Selected_LOD=Low, Content_LOD=16, Distance=120.3m, tile=Tile_2_1_0

📊 Depth Distribution: {1: 2, 2: 1}
🚫 Suppressed by children: 1
```

## 使用方法

### 1. 运行重叠避免测试
```bash
python test_overlap_avoidance.py
```

### 2. 观察关键指标
- **✅ 绿色勾号**：正常显示的Tiles
- **🚫 红色叉号**：被子Tiles抑制的父Tiles  
- **📊 Depth Distribution**：各深度层级的可见Tiles数量
- **🚫 Suppressed by children**：被抑制的Tiles数量

### 3. 验证成功标志
- 有一些Tiles被标记为"SUPPRESSED by child tile"
- 不同深度的Tiles根据距离选择了不同的LOD级别
- 没有18级别和20级别content在同一区域同时显示
- Depth Distribution显示合理的深度分布

## 技术细节

### 核心算法改进
1. **动态映射构建**：从配置文件动态构建LOD级别映射
2. **层级管理**：按深度组织Tiles，实现层级感知
3. **优先级系统**：High(4) > Medium(3) > Low(2) > VeryLow(1)
4. **空间检测**：基于边界框重叠检测空间冲突
5. **抑制机制**：高优先级子Tile抑制低优先级父Tile

### 性能影响
- **轻微增加计算开销**：需要进行重叠检测和优先级比较
- **显著减少渲染开销**：避免重复渲染重叠内容
- **提升视觉质量**：消除重叠显示的视觉问题
- **更好的内存使用**：减少同时加载的重复内容

## 调试和优化

### 如果仍有重叠问题
1. **调整重叠检测阈值**：修改`_bounds_overlap`中的`threshold`参数
2. **优化LOD优先级**：调整`lod_priority`字典中的优先级值
3. **检查边界框准确性**：验证Tile边界框计算是否正确
4. **增加调试日志**：启用`verbose=True`查看详细信息

### 配置参数
```python
# 重叠检测阈值（0.0-1.0）
overlap_threshold = 0.1  # 10%重叠即认为重叠

# LOD优先级
lod_priority = {"High": 4, "Medium": 3, "Low": 2, "VeryLow": 1}
```

## 总结

这次修复解决了18级别和20级别Tile content重叠显示的问题，通过实现：

1. **动态LOD级别映射** - 使用实际配置而非硬编码值
2. **层级感知机制** - 考虑父子Tile之间的关系
3. **重叠检测避免** - 防止空间上的重复显示
4. **优先级管理** - 确保最合适的LOD级别被显示

修复后的系统能够智能地管理不同深度Tiles的LOD选择，避免重叠显示，提供更好的视觉效果和性能表现。
