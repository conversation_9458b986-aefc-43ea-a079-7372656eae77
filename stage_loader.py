# simapp_launch_stage.py

import os
from isaacsim import SimulationApp

DISP_FPS        = 1 << 0
DISP_RESOLUTION = 1 << 3
DISP_DEV_MEM    = 1 << 13
DISP_HOST_MEM   = 1 << 14

# ---------- 2. 初始化 SimulationApp ----------
simulation_app = SimulationApp(launch_config={
    "headless": False,
    "renderer": "PathTracing",  # Can also be PathTracing
    "anti_aliasing": 0,
    "samples_per_pixel_per_frame": 1,
    "denoiser": False,
    "max_bounces": 1,
    "max_specular_transmission_bounces": 1,
    "max_volume_bounces": 1,
    "display_options": DISP_FPS | DISP_RESOLUTION | DISP_DEV_MEM | DISP_HOST_MEM,
    # "extra_args": [
    #     "--/renderer/raytracingMotion/enabled=false",
    #     "--/renderer/raytracingMotion/enableHydraEngineMasking=false",
    #     "--/renderer/raytracingMotion/enabledForHydraEngines=''",
    # ]
    })

# ---------- 3. 打开 Stage ----------
from omni.usd import get_context

usd_context = get_context()
stage_path = "E:/wanleqi/YangLaoyuan/yanglaoyuan.usd"
usd_context.open_stage(stage_path)
print(f"Stage 已加载: {stage_path}")

# ---------- 4. 获取 Stage 和主摄像机 ----------
stage = usd_context.get_stage()
from pxr import UsdGeom

camera_prim = stage.GetPrimAtPath("/World/Camera")  # 假设有 /World/Camera
if camera_prim.IsValid():
    camera = UsdGeom.Camera(camera_prim)
    print("主摄像机已获取")

# ---------- 5. 主循环 ----------
# SimulationApp 会管理仿真和渲染，需要循环更新
while simulation_app.is_running():
    simulation_app.update()

# ---------- 6. 关闭 SimulationApp ----------
simulation_app.close()
